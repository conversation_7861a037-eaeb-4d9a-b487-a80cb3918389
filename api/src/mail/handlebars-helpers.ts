import * as Handlebars from 'handlebars';

/**
 * Register custom helpers for Handlebars templates
 */
export function registerHandlebarsHelpers() {
  // Register the partial block helper for base template
  Handlebars.registerHelper('eq', function (a, b) {
    return a === b;
  });

  Handlebars.registerHelper('gt', function (a, b) {
    return a > b;
  });

  Handlebars.registerHelper('lt', function (a, b) {
    return a < b;
  });

  Handlebars.registerHelper('formatDate', function (date) {
    return new Date(date).toLocaleString();
  });

  Handlebars.registerHelper('currentYear', function () {
    return new Date().getFullYear().toString();
  });

  // Helper to add two numbers
  Handlebars.registerHelper('add', function (a, b) {
    return a + b;
  });

  // Helper to subtract two numbers
  Handlebars.registerHelper('subtract', function (a, b) {
    return a - b;
  });

  // Helper to limit array to first N items
  Handlebars.registerHelper('limit', function (array, limit) {
    if (!array || !Array.isArray(array)) return [];
    return array.slice(0, limit);
  });

  // Helper to create role breakdown for bulk deletion reports
  Handlebars.registerHelper('roleBreakdown', function (users) {
    if (!users || !Array.isArray(users)) return [];
    const counts = users.reduce((acc, user) => {
      const role = user.role || 'unknown';
      acc[role] = (acc[role] || 0) + 1;
      return acc;
    }, {});

    return Object.entries(counts).map(([role, count]) => ({ role, count }));
  });

  // Helper to truncate text
  Handlebars.registerHelper('truncate', function (str, length) {
    if (!str) return '';
    if (str.length <= length) return str;
    return str.substring(0, length) + '...';
  });
}

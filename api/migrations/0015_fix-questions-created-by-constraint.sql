-- Fix questions.created_by and quiz.created_by foreign key constraints to allow user deletion
-- Change from RESTRICT to SET NULL to prevent orphaned records

-- Fix questions table
-- Drop the existing RESTRICT constraint
ALTER TABLE "questions" DROP CONSTRAINT "questions_created_by_users_id_fk";
--> statement-breakpoint

-- Add the new SET NULL constraint and make the column nullable
ALTER TABLE "questions" ALTER COLUMN "created_by" DROP NOT NULL;
--> statement-breakpoint
ALTER TABLE "questions" ADD CONSTRAINT "questions_created_by_users_id_fk" FOREIGN KEY ("created_by") REFERENCES "public"."users"("id") ON DELETE set null ON UPDATE no action;
--> statement-breakpoint

-- Fix quiz table
-- Add foreign key constraint for quiz.created_by (it doesn't exist yet)
ALTER TABLE "quiz" ALTER COLUMN "created_by" DROP NOT NULL;
--> statement-breakpoint
ALTER TABLE "quiz" ADD CONSTRAINT "quiz_created_by_users_id_fk" FOREIGN KEY ("created_by") REFERENCES "public"."users"("id") ON DELETE set null ON UPDATE no action;
